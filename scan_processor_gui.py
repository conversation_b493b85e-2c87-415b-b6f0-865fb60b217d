import os
import datetime
import shutil
import csv
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
from PIL import Image

class ScanProcessorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("扫描数据处理工具")
        self.root.geometry("600x400")
        
        # 变量
        self.source_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="扫描数据处理工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 源文件夹选择
        ttk.Label(main_frame, text="源文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.source_folder, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_source_folder).grid(row=1, column=2, pady=5)
        
        # 输出文件夹选择
        ttk.Label(main_frame, text="输出文件夹:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_folder, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_output_folder).grid(row=2, column=2, pady=5)
        
        # 处理按钮
        self.process_button = ttk.Button(main_frame, text="开始处理", command=self.start_processing)
        self.process_button.grid(row=3, column=0, columnspan=3, pady=20)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="请选择源文件夹和输出文件夹")
        self.status_label.grid(row=5, column=0, columnspan=3, pady=5)
        
        # 日志文本框
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        self.log_text = tk.Text(log_frame, height=15, width=70)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
    def browse_source_folder(self):
        folder = filedialog.askdirectory(title="选择包含scan_*文件夹的源目录")
        if folder:
            self.source_folder.set(folder)
            self.log_message(f"选择源文件夹: {folder}")
            
    def browse_output_folder(self):
        folder = filedialog.askdirectory(title="选择输出目录")
        if folder:
            self.output_folder.set(folder)
            self.log_message(f"选择输出文件夹: {folder}")
            
    def log_message(self, message):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def start_processing(self):
        if not self.source_folder.get() or not self.output_folder.get():
            messagebox.showerror("错误", "请选择源文件夹和输出文件夹")
            return
            
        # 在新线程中运行处理
        self.process_button.config(state='disabled')
        self.progress.start()
        self.status_label.config(text="正在处理...")
        
        thread = threading.Thread(target=self.process_scans)
        thread.daemon = True
        thread.start()
        
    def process_scans(self):
        try:
            source_dir = self.source_folder.get()
            output_dir = self.output_folder.get()
            
            self.log_message(f"开始处理扫描数据...")
            self.log_message(f"源目录: {source_dir}")
            self.log_message(f"输出目录: {output_dir}")
            
            # 查找所有scan_*目录
            scan_directories = []
            for item in os.listdir(source_dir):
                item_path = os.path.join(source_dir, item)
                if os.path.isdir(item_path) and item.startswith("scan_"):
                    scan_directories.append(item_path)
            
            if not scan_directories:
                self.log_message("错误: 未找到scan_*目录")
                messagebox.showerror("错误", "在源目录中未找到scan_*目录")
                return
                
            scan_directories.sort()
            self.log_message(f"找到 {len(scan_directories)} 个scan目录")
            
            # 提取日期
            first_scan_name = os.path.basename(scan_directories[0])
            if first_scan_name.startswith("scan_"):
                parts = first_scan_name.split("_")
                if len(parts) >= 2:
                    date_part = parts[1]
                    main_date_folder = f"2025{date_part}"
                else:
                    main_date_folder = datetime.datetime.now().strftime("%Y%m%d")
            else:
                main_date_folder = datetime.datetime.now().strftime("%Y%m%d")
                
            final_output_dir = os.path.join(output_dir, main_date_folder)
            self.log_message(f"创建输出目录: {main_date_folder}")
            
            # 处理每个scan目录
            for i, scan_dir in enumerate(scan_directories, 1):
                scandata_name = f"scandata_{i}"
                self.log_message(f"处理 {os.path.basename(scan_dir)} -> {scandata_name}")
                self.process_single_scan_directory(scan_dir, final_output_dir, scandata_name)
                
            self.log_message("所有扫描目录处理完成!")
            messagebox.showinfo("完成", f"处理完成!\n输出目录: {final_output_dir}")
            
        except Exception as e:
            self.log_message(f"错误: {str(e)}")
            messagebox.showerror("错误", f"处理过程中发生错误:\n{str(e)}")
        finally:
            self.progress.stop()
            self.process_button.config(state='normal')
            self.status_label.config(text="处理完成")
            
    def process_single_scan_directory(self, source_base_dir, main_date_folder, scandata_name):
        # 提取日期用于CSV
        source_dir_name = os.path.basename(source_base_dir)
        if source_dir_name.startswith("scan_"):
            parts = source_dir_name.split("_")
            if len(parts) >= 2:
                date_part = parts[1]
            else:
                date_part = datetime.datetime.now().strftime("%m%d")
        else:
            date_part = datetime.datetime.now().strftime("%m%d")
            
        final_base_out_dir = os.path.join(main_date_folder, scandata_name)
        os.makedirs(final_base_out_dir, exist_ok=True)
        
        # 初始化CSV数据
        pose_data_for_csv = [["date", "scan", "sequence", "horizontal", "vertical"]]
        
        # 获取数字子目录
        try:
            all_source_subdirs = os.listdir(source_base_dir)
        except FileNotFoundError:
            self.log_message(f"错误: 源目录 '{source_base_dir}' 不存在")
            return
            
        numeric_source_subdirs = []
        for subdir_name in all_source_subdirs:
            if subdir_name.isdigit() and os.path.isdir(os.path.join(source_base_dir, subdir_name)):
                numeric_source_subdirs.append(subdir_name)
                
        numeric_source_subdirs.sort(key=int)
        self.log_message(f"  找到 {len(numeric_source_subdirs)} 个数字子目录")
        
        # 处理每个数字目录
        for src_folder_name in numeric_source_subdirs:
            target_seq_num = int(src_folder_name) + 1
            
            # 创建目标目录结构
            target_seq_dir = os.path.join(final_base_out_dir, str(target_seq_num))
            nir_path_target = os.path.join(target_seq_dir, "2D-NIR")
            rgb_path_target = os.path.join(target_seq_dir, "2D-RGB")
            depth_path_target = os.path.join(target_seq_dir, "3D-depth")
            
            os.makedirs(nir_path_target, exist_ok=True)
            os.makedirs(rgb_path_target, exist_ok=True)
            os.makedirs(depth_path_target, exist_ok=True)
            
            # 处理图像
            self.process_images(source_base_dir, src_folder_name, nir_path_target, rgb_path_target, depth_path_target)
            
            # 提取角度数据
            h_angle_str, v_angle_str = self.extract_angles(source_base_dir, src_folder_name)
            
            # 添加到CSV数据
            pose_data_for_csv.append([date_part, scandata_name, target_seq_num, h_angle_str, v_angle_str])
            
        # 写入pose.csv
        pose_csv_path = os.path.join(final_base_out_dir, "pose.csv")
        with open(pose_csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(pose_data_for_csv)
        self.log_message(f"  生成 pose.csv: {pose_csv_path}")

    def process_images(self, source_base_dir, src_folder_name, nir_path_target, rgb_path_target, depth_path_target):
        # 查找中间文件夹
        intermediate_folder_path = None
        path_to_list = os.path.join(source_base_dir, src_folder_name)
        if os.path.exists(path_to_list):
            for item in os.listdir(path_to_list):
                item_path = os.path.join(path_to_list, item)
                if os.path.isdir(item_path) and item not in ["params"]:
                    intermediate_folder_path = item_path
                    break

        if not intermediate_folder_path:
            self.log_message(f"    警告: 未找到中间文件夹 {path_to_list}")
            return

        # 检查ori_bmp和res_bmp目录
        src_ori_dir = os.path.join(intermediate_folder_path, "ori_bmp")
        src_res_dir = os.path.join(intermediate_folder_path, "res_bmp")

        ori_has_files = False
        if os.path.exists(src_ori_dir) and os.path.isdir(src_ori_dir):
            ori_files = [f for f in os.listdir(src_ori_dir) if f.lower().endswith(".bmp")]
            ori_has_files = len(ori_files) > 0

        res_has_files = False
        if os.path.exists(src_res_dir) and os.path.isdir(src_res_dir):
            res_files = [f for f in os.listdir(src_res_dir) if f.lower().endswith(".bmp")]
            res_has_files = len(res_files) > 0

        if ori_has_files and res_has_files:
            # NIR + Depth 模式
            self.convert_image(src_ori_dir, nir_path_target, "NIR")
            self.convert_image(src_res_dir, depth_path_target, "Depth")
        elif ori_has_files and not res_has_files:
            # RGB 模式
            self.convert_image_rgb(src_ori_dir, rgb_path_target)
        else:
            self.log_message(f"    警告: 未找到有效图像文件")

    def convert_image(self, src_dir, dest_dir, image_type):
        for item in os.listdir(src_dir):
            if item.lower().endswith(".bmp"):
                src_file_path = os.path.join(src_dir, item)
                dest_file_path = os.path.join(dest_dir, "1.jpg")
                try:
                    img = Image.open(src_file_path)
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    original_width, original_height = img.size
                    new_width = original_width // 2
                    new_height = original_height // 2
                    img = img.resize((new_width, new_height))
                    img.save(dest_file_path, "JPEG", quality=85)
                except Exception as e:
                    self.log_message(f"    错误: 转换{image_type}图像失败 {e}")
                break

    def convert_image_rgb(self, src_dir, dest_dir):
        for item in os.listdir(src_dir):
            if item.lower().endswith(".bmp"):
                src_file_path = os.path.join(src_dir, item)
                dest_file_path = os.path.join(dest_dir, "1.jpg")
                try:
                    img = Image.open(src_file_path)
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    # 先调整到1920x1080，再缩小到一半
                    img_resized_hd = img.resize((1920, 1080))
                    final_width = 1920 // 2
                    final_height = 1080 // 2
                    img_final_resized = img_resized_hd.resize((final_width, final_height))
                    img_final_resized.save(dest_file_path, "JPEG", quality=85)
                except Exception as e:
                    self.log_message(f"    错误: 转换RGB图像失败 {e}")
                break

    def extract_angles(self, source_base_dir, src_folder_name):
        params_file_path = os.path.join(source_base_dir, src_folder_name, "params")
        h_angle_str = "N/A"
        v_angle_str = "N/A"

        if os.path.exists(params_file_path):
            try:
                with open(params_file_path, 'r') as pf:
                    lines = pf.readlines()
                for line in lines:
                    stripped_line = line.strip()
                    if "H:" in stripped_line:
                        try:
                            parts = stripped_line.split("H:")
                            if len(parts) > 1:
                                h_angle_val = float(parts[1].strip())
                                h_angle_str = f"{h_angle_val:.2f}°"
                        except (ValueError, IndexError):
                            pass
                    elif "V:" in stripped_line:
                        try:
                            parts = stripped_line.split("V:")
                            if len(parts) > 1:
                                v_angle_val = float(parts[1].strip())
                                v_angle_str = f"{v_angle_val:.2f}°"
                        except (ValueError, IndexError):
                            pass
            except Exception as e:
                self.log_message(f"    错误: 读取params文件失败 {e}")

        return h_angle_str, v_angle_str

def main():
    root = tk.Tk()
    app = ScanProcessorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
