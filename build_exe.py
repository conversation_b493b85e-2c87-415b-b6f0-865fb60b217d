"""
打包脚本 - 将扫描数据处理工具打包成exe文件
使用PyInstaller进行打包

使用方法:
1. 安装依赖: pip install pyinstaller pillow
2. 运行此脚本: python build_exe.py
"""

import os
import subprocess
import sys

def install_requirements():
    """安装必要的依赖"""
    requirements = [
        'pyinstaller',
        'pillow'
    ]
    
    print("正在安装依赖...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            print(f"✓ {req} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {req} 安装失败")
            return False
    return True

def build_exe():
    """使用PyInstaller打包exe"""
    print("\n开始打包exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个exe文件
        '--windowed',                   # 不显示控制台窗口
        '--name=扫描数据处理工具',        # exe文件名
        '--icon=icon.ico',              # 图标文件(如果有的话)
        '--add-data=scan_processor_gui.py;.',  # 添加源文件
        'scan_processor_gui.py'         # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists('icon.ico'):
        cmd.remove('--icon=icon.ico')
        print("注意: 未找到icon.ico文件，将使用默认图标")
    
    try:
        subprocess.check_call(cmd)
        print("\n✓ 打包成功!")
        print("exe文件位置: dist/扫描数据处理工具.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n✗ 打包失败: {e}")
        return False

def main():
    print("=== 扫描数据处理工具 - 打包脚本 ===\n")
    
    # 检查源文件是否存在
    if not os.path.exists('scan_processor_gui.py'):
        print("错误: 未找到 scan_processor_gui.py 文件")
        return
    
    # 安装依赖
    if not install_requirements():
        print("依赖安装失败，请手动安装: pip install pyinstaller pillow")
        return
    
    # 打包exe
    if build_exe():
        print("\n打包完成! 您可以在 dist 文件夹中找到可执行文件。")
        print("\n使用说明:")
        print("1. 运行 '扫描数据处理工具.exe'")
        print("2. 选择包含scan_*文件夹的源目录")
        print("3. 选择输出目录")
        print("4. 点击'开始处理'按钮")
    else:
        print("\n打包失败，请检查错误信息。")

if __name__ == "__main__":
    main()
