#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能网关数据传输GUI应用程序
使用PyQt5创建的用户友好界面
"""

import sys
import os
import json
import threading
import time
from pathlib import Path
from typing import List, Optional

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QLineEdit, QPushButton, QTextEdit, 
    QProgressBar, QFileDialog, QListWidget, QListWidgetItem,
    QGroupBox, QSpinBox, QMessageBox, QSplitter, QFrame,
    QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt5.QtCore import (
    QThread, pyqtSignal, QTimer, Qt, QSize, QUrl, QMimeData
)
from PyQt5.QtGui import (
    QFont, QIcon, QPalette, QColor, QPixmap, QDragEnterEvent, QDropEvent, QPainter, QPen
)

from data_transmission import DataTransmissionClient


class DragDropListWidget(QListWidget):
    """支持拖拽功能的文件列表控件"""
    
    # 定义信号，当文件被拖拽进来时发出
    files_dropped = pyqtSignal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 启用拖拽功能
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DropOnly)
        
        # 设置基础样式
        self.base_style = """
            QListWidget {
                border: 2px dashed #aaa;
                border-radius: 5px;
                background-color: #fafafa;
                padding: 10px;
                min-height: 150px;
            }
            QListWidget:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
            }
        """
        
        self.drag_style = """
            QListWidget {
                border: 2px solid #4CAF50;
                border-radius: 5px;
                background-color: #e8f5e8;
                padding: 10px;
                min-height: 150px;
            }
        """
        
        self.setStyleSheet(self.base_style)
        
        # 确保拖拽模式正确设置
        self.setDefaultDropAction(Qt.CopyAction)
        
        # 添加拖拽提示文本
        self.placeholder_text = "拖拽文件到此处或使用选择按钮"
    
    def paintEvent(self, event):
        """重写绘图事件以显示占位符文本"""
        super().paintEvent(event)
        
        # 如果列表为空，显示占位符文本
        if self.count() == 0:
            painter = QPainter(self.viewport())
            painter.setPen(QPen(QColor(150, 150, 150)))
            
            # 在中央绘制提示文本
            rect = self.viewport().rect()
            painter.drawText(rect, Qt.AlignCenter | Qt.TextWordWrap, self.placeholder_text)
            painter.end()
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.setDropAction(Qt.CopyAction)
            event.accept()
            # 改变样式提示可以拖拽
            self.setStyleSheet(self.drag_style)
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        """拖拽移动事件"""
        if event.mimeData().hasUrls():
            event.setDropAction(Qt.CopyAction)
            event.accept()
        else:
            event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        # 恢复原始样式
        self.setStyleSheet(self.base_style)
        super().dragLeaveEvent(event)
    
    def dropEvent(self, event):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            files = []
            
            for url in urls:
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if os.path.isfile(file_path):
                        files.append(file_path)
            
            if files:
                # 发出信号，通知主窗口有文件被拖拽进来
                self.files_dropped.emit(files)
                event.setDropAction(Qt.CopyAction)
                event.accept()
            else:
                event.ignore()
        else:
            event.ignore()
        
        # 恢复原始样式
        self.setStyleSheet(self.base_style)


class TransmissionWorker(QThread):
    """数据传输工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(str, int, int)  # filename, current, total
    status_updated = pyqtSignal(str)  # status message
    file_completed = pyqtSignal(str, bool)  # filename, success
    transmission_completed = pyqtSignal(bool)  # overall success
    session_created = pyqtSignal(str)  # session_id
    
    def __init__(self, gateway_ip: str, device_no: str, file_paths: List[str], ftp_port: int = 21, 
                 ftp_username: str = "user1", ftp_password: str = "user1"):
        super().__init__()
        self.gateway_ip = gateway_ip
        self.device_no = device_no
        self.file_paths = file_paths
        self.ftp_port = ftp_port
        self.ftp_username = ftp_username
        self.ftp_password = ftp_password
        self.client = None
        self.is_cancelled = False
    
    def cancel(self):
        """取消传输"""
        self.is_cancelled = True
    
    def run(self):
        """执行传输任务"""
        try:
            self.status_updated.emit("🚀 开始数据传输流程...")
            
            # 创建客户端
            self.client = DataTransmissionClient(self.gateway_ip, self.device_no, self.ftp_port, self.ftp_username, self.ftp_password)
            
            # 1. 创建session
            self.status_updated.emit("1️⃣ 创建FTP传输会话...")
            session_id = self.client.create_ftp_session()
            
            if not session_id:
                self.status_updated.emit("❌ 创建FTP session失败")
                self.transmission_completed.emit(False)
                return
            
            self.session_created.emit(session_id)
            self.status_updated.emit(f"✅ Session创建成功: {session_id}")
            
            if self.is_cancelled:
                self.status_updated.emit("⚠️ 传输已取消")
                self.transmission_completed.emit(False)
                return
            
            # 2. 上传文件
            self.status_updated.emit("2️⃣ 开始文件上传...")
            success = self._upload_files_with_progress()
            
            if not success or self.is_cancelled:
                self.status_updated.emit("❌ 文件上传失败或已取消")
                self.transmission_completed.emit(False)
                return
            
            # 3. 结束session
            self.status_updated.emit("3️⃣ 结束传输会话...")
            if self.client.finish_session():
                self.status_updated.emit("✅ 传输会话已结束")
                self.status_updated.emit("🎉 数据传输流程完成!")
                self.transmission_completed.emit(True)
            else:
                self.status_updated.emit("❌ 结束会话失败")
                self.transmission_completed.emit(False)
                
        except Exception as e:
            self.status_updated.emit(f"❌ 传输过程中发生错误: {str(e)}")
            self.transmission_completed.emit(False)
    
    def _upload_files_with_progress(self) -> bool:
        """带进度的文件上传"""
        if not self.client or not self.client.current_session_id:
            return False
        
        try:
            import ftplib
            
            # 连接FTP
            ftp = ftplib.FTP()
            ftp.connect(self.gateway_ip, 21, timeout=self.client.ftp_timeout)
            ftp.login(self.client.ftp_username, self.client.ftp_password)
            ftp.set_pasv(True)
            
            # 切换目录
            target_dir = f"{self.device_no}/{self.client.current_session_id}"
            try:
                ftp.cwd(target_dir)
            except ftplib.error_perm:
                self.client._create_ftp_directory(ftp, target_dir)
                ftp.cwd(target_dir)
            
            # 上传每个文件
            for file_path in self.file_paths:
                if self.is_cancelled:
                    break
                
                if not os.path.exists(file_path):
                    self.status_updated.emit(f"⚠️ 文件不存在: {file_path}")
                    continue
                
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                uploaded_size = 0
                
                self.status_updated.emit(f"📤 开始上传: {filename} ({self.client._format_file_size(file_size)})")
                
                def upload_callback(data):
                    nonlocal uploaded_size
                    if self.is_cancelled:
                        return
                    uploaded_size += len(data)
                    self.progress_updated.emit(filename, uploaded_size, file_size)
                
                try:
                    with open(file_path, 'rb') as file:
                        ftp.storbinary(f'STOR {filename}', file, callback=upload_callback, blocksize=8192)
                    
                    if not self.is_cancelled:
                        self.file_completed.emit(filename, True)
                        self.status_updated.emit(f"✅ 上传完成: {filename}")
                    
                except Exception as e:
                    self.file_completed.emit(filename, False)
                    self.status_updated.emit(f"❌ 上传失败: {filename} - {str(e)}")
                    return False
            
            ftp.quit()
            return not self.is_cancelled
            
        except Exception as e:
            self.status_updated.emit(f"❌ FTP连接错误: {str(e)}")
            return False


class DataTransmissionGUI(QMainWindow):
    """数据传输GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.file_paths = []
        
        # 启用主窗口的拖拽支持
        self.setAcceptDrops(True)
        
        self.init_ui()
        self.setup_styles()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("智能网关数据传输工具 v1.0")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
    
    def create_left_panel(self) -> QWidget:
        """创建左侧配置面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 连接配置组
        conn_group = QGroupBox("连接配置")
        conn_layout = QGridLayout(conn_group)
        
        # 第一行：网关IP（占据全宽）
        conn_layout.addWidget(QLabel("网关IP:"), 0, 0)
        self.gateway_ip_edit = QLineEdit("*************")
        conn_layout.addWidget(self.gateway_ip_edit, 0, 1, 1, 3)  # 跨越3列
        
        # 第二行：设备编号 和 FTP用户名
        conn_layout.addWidget(QLabel("设备编号:"), 1, 0)
        self.device_no_edit = QLineEdit("lai123")
        conn_layout.addWidget(self.device_no_edit, 1, 1)
        
        conn_layout.addWidget(QLabel("FTP用户名:"), 1, 2)
        self.ftp_username_edit = QLineEdit("user1")
        conn_layout.addWidget(self.ftp_username_edit, 1, 3)
        
        # 第三行：FTP端口 和 FTP密码
        conn_layout.addWidget(QLabel("FTP端口:"), 2, 0)
        self.ftp_port_spin = QSpinBox()
        self.ftp_port_spin.setRange(1, 65535)
        self.ftp_port_spin.setValue(21)
        conn_layout.addWidget(self.ftp_port_spin, 2, 1)
        
        conn_layout.addWidget(QLabel("FTP密码:"), 2, 2)
        self.ftp_password_edit = QLineEdit("user1")
        self.ftp_password_edit.setEchoMode(QLineEdit.Password)  # 密码输入模式
        conn_layout.addWidget(self.ftp_password_edit, 2, 3)
        
        layout.addWidget(conn_group)
        
        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
    
        
        # 文件选择按钮
        btn_layout = QHBoxLayout()
        self.select_files_btn = QPushButton("📁 选择文件")
        self.select_files_btn.clicked.connect(self.select_files)
        
        self.clear_files_btn = QPushButton("🗑️ 清空列表")
        self.clear_files_btn.clicked.connect(self.clear_files)
        
        btn_layout.addWidget(self.select_files_btn)
        btn_layout.addWidget(self.clear_files_btn)
        file_layout.addLayout(btn_layout)
        
        # 文件列表（支持拖拽）
        self.file_list = DragDropListWidget()
        self.file_list.setMaximumHeight(200)
        self.file_list.files_dropped.connect(self.on_files_dropped)
        file_layout.addWidget(self.file_list)
        
        layout.addWidget(file_group)
        
        # 控制按钮组
        control_group = QGroupBox("传输控制")
        control_layout = QVBoxLayout(control_group)
        
        self.start_btn = QPushButton("🚀 开始传输")
        self.start_btn.clicked.connect(self.start_transmission)
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        
        self.cancel_btn = QPushButton("⏹️ 取消传输")
        self.cancel_btn.clicked.connect(self.cancel_transmission)
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 10px; }")
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.cancel_btn)
        
        layout.addWidget(control_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def create_right_panel(self) -> QWidget:
        """创建右侧状态面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 进度标签页
        progress_tab = QWidget()
        progress_layout = QVBoxLayout(progress_tab)
        
        # 总体进度
        overall_group = QGroupBox("总体进度")
        overall_layout = QVBoxLayout(overall_group)
        
        self.overall_progress = QProgressBar()
        self.overall_progress.setVisible(False)
        overall_layout.addWidget(self.overall_progress)
        
        self.session_label = QLabel("会话ID: 未创建")
        overall_layout.addWidget(self.session_label)
        
        progress_layout.addWidget(overall_group)
        
        # 文件进度表格
        files_group = QGroupBox("文件传输进度")
        files_layout = QVBoxLayout(files_group)
        
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(4)
        self.file_table.setHorizontalHeaderLabels(["文件名", "大小", "进度", "状态"])
        
        # 设置表格列宽
        header = self.file_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        files_layout.addWidget(self.file_table)
        progress_layout.addWidget(files_group)
        
        tab_widget.addTab(progress_tab, "📊 传输进度")
        
        # 日志标签页
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        
        log_group = QGroupBox("传输日志")
        log_group_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        log_group_layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_btn_layout = QHBoxLayout()
        self.clear_log_btn = QPushButton("🗑️ 清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        
        self.save_log_btn = QPushButton("💾 保存日志")
        self.save_log_btn.clicked.connect(self.save_log)
        
        log_btn_layout.addWidget(self.clear_log_btn)
        log_btn_layout.addWidget(self.save_log_btn)
        log_btn_layout.addStretch()
        
        log_group_layout.addLayout(log_btn_layout)
        log_layout.addWidget(log_group)
        
        tab_widget.addTab(log_tab, "📝 日志信息")
        
        layout.addWidget(tab_widget)
        
        return panel
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                border: none;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QLineEdit, QSpinBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
            }
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }
        """)
    
    def select_files(self):
        """选择文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, 
            "选择要传输的文件", 
            "", 
            "所有文件 (*);;压缩文件 (*.zip *.rar *.7z);;图像文件 (*.jpg *.jpeg *.png *.bmp);;数据文件 (*.las *.txt *.json)"
        )
        
        if files:
            self.add_files_to_list(files)
    
    def on_files_dropped(self, file_paths):
        """处理拖拽的文件"""
        self.add_files_to_list(file_paths)
        self.log_message(f"📁 通过拖拽添加了 {len(file_paths)} 个文件")
    
    def add_files_to_list(self, file_paths):
        """添加文件到列表"""
        new_files_count = 0
        
        for file_path in file_paths:
            if file_path not in self.file_paths:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    self.log_message(f"⚠️ 文件不存在，已跳过: {os.path.basename(file_path)}")
                    continue
                
                self.file_paths.append(file_path)
                new_files_count += 1
                
                # 添加到列表显示
                item = QListWidgetItem()
                file_size = os.path.getsize(file_path)
                size_str = self._format_file_size(file_size)
                filename = os.path.basename(file_path)
                
                # 根据文件大小设置不同的显示颜色
                if file_size > 100 * 1024 * 1024:  # 大于100MB
                    item.setText(f"🗜️ {filename} ({size_str})")
                elif file_size > 10 * 1024 * 1024:  # 大于10MB
                    item.setText(f"📦 {filename} ({size_str})")
                else:
                    item.setText(f"📄 {filename} ({size_str})")
                
                item.setToolTip(f"完整路径: {file_path}")
                self.file_list.addItem(item)
        
        if new_files_count > 0:
            self.log_message(f"📁 已添加 {new_files_count} 个新文件，总计 {len(self.file_paths)} 个文件")
        elif len(file_paths) > 0:
            self.log_message("ℹ️ 所选文件已在列表中，未添加重复文件")
    
    def clear_files(self):
        """清空文件列表"""
        self.file_paths.clear()
        self.file_list.clear()
        self.file_table.setRowCount(0)
        self.log_message("🗑️ 已清空文件列表")
    
    def start_transmission(self):
        """开始传输"""
        if not self.file_paths:
            QMessageBox.warning(self, "警告", "请先选择要传输的文件！")
            return
        
        gateway_ip = self.gateway_ip_edit.text().strip()
        device_no = self.device_no_edit.text().strip()
        ftp_username = self.ftp_username_edit.text().strip()
        ftp_password = self.ftp_password_edit.text().strip()
        
        if not gateway_ip or not device_no:
            QMessageBox.warning(self, "警告", "请填写网关IP和设备编号！")
            return
        
        if not ftp_username or not ftp_password:
            QMessageBox.warning(self, "警告", "请填写FTP用户名和密码！")
            return
        
        # 初始化文件进度表格
        self.setup_file_table()
        
        # 创建工作线程
        self.worker = TransmissionWorker(
            gateway_ip, 
            device_no, 
            self.file_paths.copy(),
            self.ftp_port_spin.value(),
            ftp_username,
            ftp_password
        )
        
        # 连接信号
        self.worker.progress_updated.connect(self.update_file_progress)
        self.worker.status_updated.connect(self.log_message)
        self.worker.file_completed.connect(self.file_completed)
        self.worker.transmission_completed.connect(self.transmission_completed)
        self.worker.session_created.connect(self.session_created)
        
        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.overall_progress.setVisible(True)
        self.overall_progress.setRange(0, 0)  # 不确定进度
        
        # 启动线程
        self.worker.start()
        
        self.log_message("🚀 开始数据传输...")
        self.statusBar().showMessage("传输中...")
    
    def cancel_transmission(self):
        """取消传输"""
        if self.worker and self.worker.isRunning():
            self.worker.cancel()
            self.log_message("⚠️ 正在取消传输...")
            self.cancel_btn.setEnabled(False)
    
    def setup_file_table(self):
        """设置文件进度表格"""
        self.file_table.setRowCount(len(self.file_paths))
        
        for i, file_path in enumerate(self.file_paths):
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            size_str = self._format_file_size(file_size)
            
            # 文件名
            self.file_table.setItem(i, 0, QTableWidgetItem(filename))
            
            # 文件大小
            self.file_table.setItem(i, 1, QTableWidgetItem(size_str))
            
            # 进度条
            progress = QProgressBar()
            progress.setRange(0, 100)
            progress.setValue(0)
            self.file_table.setCellWidget(i, 2, progress)
            
            # 状态
            self.file_table.setItem(i, 3, QTableWidgetItem("等待中"))
    
    def update_file_progress(self, filename: str, current: int, total: int):
        """更新文件进度"""
        # 找到对应的行
        for i in range(self.file_table.rowCount()):
            if self.file_table.item(i, 0).text() == filename:
                progress_bar = self.file_table.cellWidget(i, 2)
                if progress_bar:
                    percentage = int((current / total) * 100) if total > 0 else 0
                    progress_bar.setValue(percentage)
                
                # 更新状态
                status_item = self.file_table.item(i, 3)
                if status_item:
                    status_item.setText(f"上传中 {percentage}%")
                break
    
    def file_completed(self, filename: str, success: bool):
        """文件完成"""
        for i in range(self.file_table.rowCount()):
            if self.file_table.item(i, 0).text() == filename:
                status_item = self.file_table.item(i, 3)
                if status_item:
                    if success:
                        status_item.setText("✅ 完成")
                        progress_bar = self.file_table.cellWidget(i, 2)
                        if progress_bar:
                            progress_bar.setValue(100)
                    else:
                        status_item.setText("❌ 失败")
                break
    
    def session_created(self, session_id: str):
        """会话创建"""
        self.session_label.setText(f"会话ID: {session_id}")
    
    def transmission_completed(self, success: bool):
        """传输完成"""
        self.start_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.overall_progress.setVisible(False)
        
        if success:
            self.statusBar().showMessage("传输完成")
            QMessageBox.information(self, "成功", "🎉 数据传输完成！")
        else:
            self.statusBar().showMessage("传输失败")
            QMessageBox.warning(self, "失败", "❌ 数据传输失败，请查看日志了解详情。")
        
        self.session_label.setText("会话ID: 未创建")
    
    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def save_log(self):
        """保存日志"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存日志文件", f"transmission_log_{int(time.time())}.txt", "文本文件 (*.txt)"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "成功", f"日志已保存到: {filename}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存日志失败: {str(e)}")
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小显示"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024**2:
            return f"{size_bytes/1024:.1f} KB"
        elif size_bytes < 1024**3:
            return f"{size_bytes/(1024**2):.1f} MB"
        else:
            return f"{size_bytes/(1024**3):.1f} GB"
    
    def dragEnterEvent(self, event):
        """主窗口拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        """主窗口拖拽移动事件"""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()
    
    def dropEvent(self, event):
        """主窗口拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            files = []
            
            for url in urls:
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if os.path.isfile(file_path):
                        files.append(file_path)
            
            if files:
                # 直接添加文件到列表
                self.add_files_to_list(files)
                self.log_message(f"📁 通过拖拽到窗口添加了 {len(files)} 个文件")
                event.accept()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self, "确认", "传输正在进行中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.worker.cancel()
                self.worker.wait(3000)  # 等待3秒
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("智能网关数据传输工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("LAI-MAP")
    
    # 创建并显示主窗口
    window = DataTransmissionGUI()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 