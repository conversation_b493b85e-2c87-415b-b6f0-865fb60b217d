import os
import datetime
import shutil
import csv
import random
from PIL import Image # Added for PNG conversion

def process_single_scan_directory(source_base_dir):
    """Process a single scan directory"""
    print(f"\n=== Processing scan directory: {source_base_dir} ===")

    # Extract date from source directory name (e.g., scan_0708_223037 -> 0708 -> 20250708)
    source_dir_name = os.path.basename(source_base_dir)
    if source_dir_name.startswith("scan_"):
        parts = source_dir_name.split("_")
        if len(parts) >= 2:
            date_part = parts[1]  # e.g., "0708"
            # Convert to full date format (assuming 2025)
            date_str = f"2025{date_part}"  # e.g., "20250708"
        else:
            date_str = datetime.datetime.now().strftime("%Y%m%d")  # fallback
    else:
        date_str = datetime.datetime.now().strftime("%Y%m%d")  # fallback

    target_main_dir_name = date_str
    target_scandata_name = "scandata_1"

    final_base_out_dir = os.path.join(target_main_dir_name, target_scandata_name)

    # 2. Create base output directories
    os.makedirs(final_base_out_dir, exist_ok=True)
    print(f"Created base directory: {final_base_out_dir}")

    # 3. Initialize data collectors for CSV files
    pose_data_for_csv = [["date", "scan", "sequence", "horizontal", "vertical"]]
    lai_data_for_csv = [["date", "scan", "sequence", "LAI"]]

    # 4. List and filter source subdirectories
    try:
        all_source_subdirs = os.listdir(source_base_dir)
    except FileNotFoundError:
        print(f"Error: Source directory '{source_base_dir}' not found.")
        return

    numeric_source_subdirs = []
    for subdir_name in all_source_subdirs:
        if subdir_name.isdigit() and os.path.isdir(os.path.join(source_base_dir, subdir_name)):
            numeric_source_subdirs.append(subdir_name)
    
    # Sort numerically
    numeric_source_subdirs.sort(key=int)
    print(f"Found {len(numeric_source_subdirs)} numeric source subdirectories.")

    # 5. Process each source subdirectory
    for src_folder_name in numeric_source_subdirs:
        try:
            int_src_folder_num = int(src_folder_name)
            target_seq_num = int_src_folder_num + 1
            
            print(f"Processing source folder: {src_folder_name} -> target sequence: {target_seq_num}")

            target_seq_folder_path = os.path.join(final_base_out_dir, str(target_seq_num))
            
            nir_path_target = os.path.join(target_seq_folder_path, "2D-NIR")
            rgb_path_target = os.path.join(target_seq_folder_path, "2D-RGB") # Empty as per requirement
            depth_path_target = os.path.join(target_seq_folder_path, "3D-depth")

            os.makedirs(nir_path_target, exist_ok=True)
            os.makedirs(rgb_path_target, exist_ok=True)
            os.makedirs(depth_path_target, exist_ok=True)

            # --- SECTION for image processing ---
            intermediate_folder_path = None
            path_to_list = os.path.join(source_base_dir, src_folder_name)
            if os.path.exists(path_to_list):
                for item in os.listdir(path_to_list):
                    item_path = os.path.join(path_to_list, item)
                    if os.path.isdir(item_path) and item not in ["params"]: # Look for intermediate folder
                        intermediate_folder_path = item_path
                        print(f"  Found intermediate folder: {intermediate_folder_path}")
                        break

            if intermediate_folder_path:
                # Check what directories exist to determine image types
                src_ori_dir = os.path.join(intermediate_folder_path, "ori_bmp")
                src_res_dir = os.path.join(intermediate_folder_path, "res_bmp")

                # Check if ori_bmp has files
                ori_has_files = False
                if os.path.exists(src_ori_dir) and os.path.isdir(src_ori_dir):
                    ori_files = [f for f in os.listdir(src_ori_dir) if f.lower().endswith(".bmp")]
                    ori_has_files = len(ori_files) > 0

                # Check if res_bmp has files
                res_has_files = False
                if os.path.exists(src_res_dir) and os.path.isdir(src_res_dir):
                    res_files = [f for f in os.listdir(src_res_dir) if f.lower().endswith(".bmp")]
                    res_has_files = len(res_files) > 0

                print(f"  ori_bmp has files: {ori_has_files}, res_bmp has files: {res_has_files}")

                if ori_has_files and res_has_files:
                    # Both have files: ori -> NIR, res -> depth
                    print("  Processing as NIR + Depth mode")

                    # Process ori_bmp as 2D-NIR
                    for item in os.listdir(src_ori_dir):
                        if item.lower().endswith(".bmp"):
                            src_file_path = os.path.join(src_ori_dir, item)
                            dest_file_path = os.path.join(nir_path_target, "1.jpg")
                            try:
                                img = Image.open(src_file_path)
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                                original_width, original_height = img.size
                                new_width = original_width // 2
                                new_height = original_height // 2
                                img = img.resize((new_width, new_height))
                                img.save(dest_file_path, "JPEG", quality=85)
                                print(f"  Converted and saved NIR: {src_file_path} to {dest_file_path}")
                            except Exception as e_conv:
                                print(f"  Error converting NIR image {src_file_path}: {e_conv}")
                            break

                    # Process res_bmp as 3D-depth
                    for item in os.listdir(src_res_dir):
                        if item.lower().endswith(".bmp"):
                            src_file_path = os.path.join(src_res_dir, item)
                            dest_file_path = os.path.join(depth_path_target, "1.jpg")
                            try:
                                img = Image.open(src_file_path)
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                                original_width, original_height = img.size
                                new_width = original_width // 2
                                new_height = original_height // 2
                                img = img.resize((new_width, new_height))
                                img.save(dest_file_path, "JPEG", quality=85)
                                print(f"  Converted and saved Depth: {src_file_path} to {dest_file_path}")
                            except Exception as e_conv:
                                print(f"  Error converting Depth image {src_file_path}: {e_conv}")
                            break

                elif ori_has_files and not res_has_files:
                    # Only ori has files: ori -> RGB
                    print("  Processing as RGB mode (only ori_bmp has files)")

                    # Process ori_bmp as 2D-RGB
                    for item in os.listdir(src_ori_dir):
                        if item.lower().endswith(".bmp"):
                            src_file_path = os.path.join(src_ori_dir, item)
                            dest_file_path = os.path.join(rgb_path_target, "1.jpg")
                            try:
                                img = Image.open(src_file_path)
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                                # First resize to 1920x1080, then to half (960x540)
                                img_resized_hd = img.resize((1920, 1080))
                                final_width = 1920 // 2
                                final_height = 1080 // 2
                                img_final_resized = img_resized_hd.resize((final_width, final_height))
                                img_final_resized.save(dest_file_path, "JPEG", quality=85)
                                print(f"  Converted and saved RGB: {src_file_path} to {dest_file_path}")
                            except Exception as e_conv:
                                print(f"  Error converting RGB image {src_file_path}: {e_conv}")
                            break
                else:
                    print(f"  Warning: No valid image files found in ori_bmp or unexpected file structure")
            else:
                print(f"  Warning: Could not find intermediate folder in {path_to_list}")
            # --- END SECTION for image processing---



            # Parse params file
            params_file_path = os.path.join(source_base_dir, src_folder_name, "params")
            h_angle_str = "N/A"
            v_angle_str = "N/A"
            if os.path.exists(params_file_path):
                with open(params_file_path, 'r') as pf:
                    lines = pf.readlines()
                for line in lines:
                    stripped_line = line.strip()
                    if "H:" in stripped_line:
                        try:
                            # Ensure "H:" is a distinct token, not part of a larger word.
                            parts = stripped_line.split("H:")
                            if len(parts) > 1:
                                h_angle_val = float(parts[1].strip())
                                h_angle_str = f"{h_angle_val:.2f}°"
                        except (ValueError, IndexError) as e_parse:
                            print(f"  Warning: Could not parse H angle from line: '{stripped_line}' in {params_file_path}. Error: {e_parse}")
                    elif "V:" in stripped_line:
                        try:
                            # Ensure "V:" is a distinct token
                            parts = stripped_line.split("V:")
                            if len(parts) > 1:
                                v_angle_val = float(parts[1].strip())
                                v_angle_str = f"{v_angle_val:.2f}°"
                        except (ValueError, IndexError) as e_parse:
                             print(f"  Warning: Could not parse V angle from line: '{stripped_line}' in {params_file_path}. Error: {e_parse}")
            else:
                print(f"  Warning: Params file not found: {params_file_path}")
            
            # Generate random LAI
            lai_val = round(random.uniform(0.0, 1.0), 2) # Changed 7.0 to 1.0, As per example 0.1, using 2 decimal places

            # Use the extracted date for CSV (convert back to MMDD format for consistency)
            csv_date = date_str[4:] if len(date_str) == 8 else date_str  # e.g., "20250708" -> "0708"
            pose_data_for_csv.append([csv_date, target_scandata_name, target_seq_num, h_angle_str, v_angle_str])
            lai_data_for_csv.append([csv_date, target_scandata_name, target_seq_num, lai_val])

        except Exception as e:
            print(f"Error processing folder {src_folder_name}: {e}")
            continue # Skip to next folder on error

    # 6. Write pose.csv
    pose_csv_path = os.path.join(final_base_out_dir, "pose.csv")
    with open(pose_csv_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(pose_data_for_csv)
    print(f"Generated pose.csv at: {pose_csv_path}")

    # 7. Write LAI.csv
    lai_csv_path = os.path.join(final_base_out_dir, "LAI.csv")
    with open(lai_csv_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(lai_data_for_csv)
    print(f"Generated LAI.csv at: {lai_csv_path}")

    print(f"Finished processing scan directory: {source_base_dir}")

def create_structure_and_copy_files():
    """Main function to process all scan directories in D:/0708"""
    base_dir = "D:/0708"

    print(f"Scanning for scan directories in: {base_dir}")

    if not os.path.exists(base_dir):
        print(f"Error: Base directory '{base_dir}' not found.")
        return

    # Find all scan_* directories
    scan_directories = []
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path) and item.startswith("scan_"):
            scan_directories.append(item_path)

    if not scan_directories:
        print(f"No scan directories found in {base_dir}")
        return

    # Sort directories for consistent processing order
    scan_directories.sort()

    print(f"Found {len(scan_directories)} scan directories:")
    for scan_dir in scan_directories:
        print(f"  - {os.path.basename(scan_dir)}")

    # Process each scan directory
    for scan_dir in scan_directories:
        try:
            process_single_scan_directory(scan_dir)
        except Exception as e:
            print(f"Error processing {scan_dir}: {e}")
            continue

    print("\n=== All scan directories processed ===")

if __name__ == "__main__":
    create_structure_and_copy_files()