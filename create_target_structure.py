import os
import datetime
import shutil
import csv
import random
from PIL import Image # Added for PNG conversion

def create_structure_and_copy_files():
    # 1. Get current date and define base directories
    date_str = datetime.datetime.now().strftime("%Y%m%d")
    source_base_dir = "D:/新建文件夹/scan_0620_190420"
    
    target_main_dir_name = date_str
    target_scandata_name = "scandata_1"
    
    final_base_out_dir = os.path.join(target_main_dir_name, target_scandata_name)

    # 2. Create base output directories
    os.makedirs(final_base_out_dir, exist_ok=True)
    print(f"Created base directory: {final_base_out_dir}")

    # 3. Initialize data collectors for CSV files
    pose_data_for_csv = [["date", "scan", "sequence", "horizontal", "vertical"]]
    lai_data_for_csv = [["date", "scan", "sequence", "LAI"]]

    # 4. List and filter source subdirectories
    try:
        all_source_subdirs = os.listdir(source_base_dir)
    except FileNotFoundError:
        print(f"Error: Source directory '{source_base_dir}' not found.")
        return

    numeric_source_subdirs = []
    for subdir_name in all_source_subdirs:
        if subdir_name.isdigit() and os.path.isdir(os.path.join(source_base_dir, subdir_name)):
            numeric_source_subdirs.append(subdir_name)
    
    # Sort numerically
    numeric_source_subdirs.sort(key=int)
    print(f"Found {len(numeric_source_subdirs)} numeric source subdirectories.")

    # 5. Process each source subdirectory
    for src_folder_name in numeric_source_subdirs:
        try:
            int_src_folder_num = int(src_folder_name)
            target_seq_num = int_src_folder_num + 1
            
            print(f"Processing source folder: {src_folder_name} -> target sequence: {target_seq_num}")

            target_seq_folder_path = os.path.join(final_base_out_dir, str(target_seq_num))
            
            nir_path_target = os.path.join(target_seq_folder_path, "2D-NIR")
            rgb_path_target = os.path.join(target_seq_folder_path, "2D-RGB") # Empty as per requirement
            depth_path_target = os.path.join(target_seq_folder_path, "3D-depth")

            os.makedirs(nir_path_target, exist_ok=True)
            os.makedirs(rgb_path_target, exist_ok=True)
            os.makedirs(depth_path_target, exist_ok=True)

            # --- SECTION for gated images ---
            intermediate_folder_path = None
            path_to_list = os.path.join(source_base_dir, src_folder_name)
            if os.path.exists(path_to_list):
                for item in os.listdir(path_to_list):
                    item_path = os.path.join(path_to_list, item)
                    if os.path.isdir(item_path) and item not in ["raw_bmp", "res_bmp"]: # Basic check
                        intermediate_folder_path = item_path
                        print(f"  Found intermediate folder: {intermediate_folder_path}")
                        break 
            
            if intermediate_folder_path:
                # Copy 2D-NIR image (from ori_bmp, formerly assumed raw_bmp)
                src_nir_dir = os.path.join(intermediate_folder_path, "ori_bmp") # Changed "raw_bmp" to "ori_bmp"
                if os.path.exists(src_nir_dir) and os.path.isdir(src_nir_dir):
                    found_nir_bmp = False
                    for item in os.listdir(src_nir_dir):
                        if item.lower().endswith(".bmp"):
                            src_file_path = os.path.join(src_nir_dir, item)
                            dest_file_path = os.path.join(nir_path_target, "1.jpg") # Changed to .jpg
                            try:
                                img = Image.open(src_file_path)
                                # Ensure image is in RGB mode for JPEG saving if it's not (e.g. P mode for some BMPs)
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                                # Resize to half original dimensions
                                original_width, original_height = img.size
                                new_width = original_width // 2
                                new_height = original_height // 2
                                img = img.resize((new_width, new_height))
                                img.save(dest_file_path, "JPEG", quality=85) 
                                print(f"  Converted, Resized (to {new_width}x{new_height}), and Copied NIR: {src_file_path} to {dest_file_path}")
                            except Exception as e_conv:
                                print(f"  Error converting/resizing/saving NIR image {src_file_path} to JPG: {e_conv}")
                            found_nir_bmp = True 
                            break 
                    if not found_nir_bmp:
                        print(f"  Warning: No .bmp file found in {src_nir_dir}")
                else:
                    print(f"  Warning: NIR source directory not found or not a dir: {src_nir_dir}")

                # Copy 3D-depth image (from res_bmp)
                src_depth_dir = os.path.join(intermediate_folder_path, "res_bmp")
                if os.path.exists(src_depth_dir) and os.path.isdir(src_depth_dir):
                    found_depth_bmp = False
                    for item in os.listdir(src_depth_dir):
                        if item.lower().endswith(".bmp"):
                            src_file_path = os.path.join(src_depth_dir, item)
                            dest_file_path = os.path.join(depth_path_target, "1.jpg") # Changed to .jpg
                            try:
                                img = Image.open(src_file_path)
                                # Ensure image is in RGB mode for JPEG saving
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                                # Resize to half original dimensions
                                original_width, original_height = img.size
                                new_width = original_width // 2
                                new_height = original_height // 2
                                img = img.resize((new_width, new_height))
                                img.save(dest_file_path, "JPEG", quality=85) 
                                print(f"  Converted, Resized (to {new_width}x{new_height}), and Copied Depth: {src_file_path} to {dest_file_path}")
                            except Exception as e_conv:
                                print(f"  Error converting/resizing/saving Depth image {src_file_path} to JPG: {e_conv}")
                            found_depth_bmp = True
                            break
                    if not found_depth_bmp:
                        print(f"  Warning: No .bmp file found in {src_depth_dir}")
                else:
                    print(f"  Warning: Depth source directory not found or not a dir: {src_depth_dir}")
            else:
                print(f"  Warning: Could not find intermediate folder in {path_to_list} for images.")
            # --- END SECTION for gated images---

            # --- SECTION for RGB images ---
            rgb_source_base_dir = "RGB1"
            rgb_src_numeric_folder_path = os.path.join(rgb_source_base_dir, src_folder_name) # e.g., RGB1/0
            
            rgb_intermediate_folder_path = None
            if os.path.exists(rgb_src_numeric_folder_path) and os.path.isdir(rgb_src_numeric_folder_path):
                for item in os.listdir(rgb_src_numeric_folder_path):
                    item_path = os.path.join(rgb_src_numeric_folder_path, item)
                    # Assuming intermediate folder is a directory and not named 'alt_bmp' or 'params'
                    if os.path.isdir(item_path) and item.lower() != "alt_bmp" and item.lower() != "params":
                        rgb_intermediate_folder_path = item_path
                        print(f"  Found RGB intermediate folder: {rgb_intermediate_folder_path}")
                        break
            
            if rgb_intermediate_folder_path:
                src_rgb_dir = os.path.join(rgb_intermediate_folder_path, "alt_bmp")
                if os.path.exists(src_rgb_dir) and os.path.isdir(src_rgb_dir):
                    found_rgb_bmp = False
                    for item in os.listdir(src_rgb_dir):
                        if item.lower().endswith(".bmp"):
                            src_file_path = os.path.join(src_rgb_dir, item)
                            dest_file_path = os.path.join(rgb_path_target, "1.jpg") # rgb_path_target defined earlier
                            try:
                                img = Image.open(src_file_path)
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                                # First resize to 1920x1080
                                img_resized_hd = img.resize((1920, 1080))
                                # Then resize to half of that (960x540)
                                final_width = 1920 // 2
                                final_height = 1080 // 2
                                img_final_resized = img_resized_hd.resize((final_width, final_height))
                                img_final_resized.save(dest_file_path, "JPEG", quality=85)
                                print(f"  Converted, Resized (to {final_width}x{final_height}), and Copied RGB: {src_file_path} to {dest_file_path}")
                            except Exception as e_conv:
                                print(f"  Error converting/resizing/saving RGB image {src_file_path} to JPG: {e_conv}")
                            found_rgb_bmp = True
                            break
                    if not found_rgb_bmp:
                        print(f"  Warning: No .bmp file found in {src_rgb_dir} for RGB.")
                else:
                    print(f"  Warning: RGB source directory ('{src_rgb_dir}') not found or not a dir.")
            elif os.path.exists(rgb_src_numeric_folder_path):
                 print(f"  Warning: Could not find intermediate folder in {rgb_src_numeric_folder_path} for RGB images.")
            else:
                print(f"  Warning: RGB numeric source folder '{rgb_src_numeric_folder_path}' not found.")
            # --- END SECTION for RGB images ---

            # Parse params file
            params_file_path = os.path.join(source_base_dir, src_folder_name, "params")
            h_angle_str = "N/A"
            v_angle_str = "N/A"
            if os.path.exists(params_file_path):
                with open(params_file_path, 'r') as pf:
                    lines = pf.readlines()
                for line in lines:
                    stripped_line = line.strip()
                    if "H:" in stripped_line:
                        try:
                            # Ensure "H:" is a distinct token, not part of a larger word.
                            parts = stripped_line.split("H:")
                            if len(parts) > 1:
                                h_angle_val = float(parts[1].strip())
                                h_angle_str = f"{h_angle_val:.2f}°"
                        except (ValueError, IndexError) as e_parse:
                            print(f"  Warning: Could not parse H angle from line: '{stripped_line}' in {params_file_path}. Error: {e_parse}")
                    elif "V:" in stripped_line:
                        try:
                            # Ensure "V:" is a distinct token
                            parts = stripped_line.split("V:")
                            if len(parts) > 1:
                                v_angle_val = float(parts[1].strip())
                                v_angle_str = f"{v_angle_val:.2f}°"
                        except (ValueError, IndexError) as e_parse:
                             print(f"  Warning: Could not parse V angle from line: '{stripped_line}' in {params_file_path}. Error: {e_parse}")
            else:
                print(f"  Warning: Params file not found: {params_file_path}")
            
            # Generate random LAI
            lai_val = round(random.uniform(0.0, 1.0), 2) # Changed 7.0 to 1.0, As per example 0.1, using 2 decimal places

            pose_data_for_csv.append([date_str, target_scandata_name, target_seq_num, h_angle_str, v_angle_str])
            lai_data_for_csv.append([date_str, target_scandata_name, target_seq_num, lai_val])

        except Exception as e:
            print(f"Error processing folder {src_folder_name}: {e}")
            continue # Skip to next folder on error

    # 6. Write pose.csv
    pose_csv_path = os.path.join(final_base_out_dir, "pose.csv")
    with open(pose_csv_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(pose_data_for_csv)
    print(f"Generated pose.csv at: {pose_csv_path}")

    # 7. Write LAI.csv
    lai_csv_path = os.path.join(final_base_out_dir, "LAI.csv")
    with open(lai_csv_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(lai_data_for_csv)
    print(f"Generated LAI.csv at: {lai_csv_path}")

    print("Script finished.")

if __name__ == "__main__":
    create_structure_and_copy_files() 