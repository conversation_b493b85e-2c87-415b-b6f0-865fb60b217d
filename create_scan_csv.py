import os
import csv
import json
import argparse
from datetime import datetime # Added for timestamp formatting

# Path to the data_format.json file, assuming it's in the same directory as the script
# or in the workspace root. This is now the fixed path.
DATA_FORMAT_JSON_PATH = "data_format.json"

def load_and_flatten_health_data():
    """Loads the health data from data_format.json (fixed path) and flattens it."""
    try:
        with open(DATA_FORMAT_JSON_PATH, 'r', encoding='utf-8') as f:
            health_data_template = json.load(f)
    except FileNotFoundError:
        print(f"Error: '{DATA_FORMAT_JSON_PATH}' not found. Please ensure it's in the correct location.")
        return None, None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{DATA_FORMAT_JSON_PATH}'.")
        return None, None

    flat_health_headers = []
    flat_health_values = []
    for main_key, sub_dict in health_data_template.items():
        for sub_key, value in sub_dict.items():
            flat_health_headers.append(f"{main_key}_{sub_key}")
            flat_health_values.append(value)
    return flat_health_headers, flat_health_values

def process_scan_data(input_dir, output_csv_file):
    """
    Processes scan data from the input directory and generates a summary CSV file.
    Each row in the output CSV corresponds to a PTZ pose from pose.csv files,
    augmented with constant health data from data_format.json and a pose.csv modification timestamp.
    """
    flat_health_headers, flat_health_values = load_and_flatten_health_data()
    if flat_health_headers is None:
        return

    # Base headers from pose.csv, plus the new timestamp column
    base_headers = ["date", "scan_folder", "sequence", "horizontal", "vertical", "pose_file_timestamp"]
    csv_headers = base_headers + flat_health_headers
    output_rows = []

    if not os.path.isdir(input_dir):
        print(f"Error: Input directory '{input_dir}' not found or is not a directory.")
        return

    # Determine if the input_dir itself is the date_folder or contains date_folders
    # This allows flexibility if user passes "20250604" or "path/to/20250604"
    # or if user passes a parent dir like "scans_archive" which contains "20250604", "20250605"
    
    potential_date_folders = []
    # Check if input_dir itself looks like a date folder (e.g., "20250604")
    # A simple check could be if its name is all digits and has a certain length, e.g. 8 for YYYYMMDD
    # Or, more robustly, try to see if it contains scandata_* folders.
    # For now, let's assume if it contains scandata_* it's a date folder.
    # If not, then its subdirectories are the date_folders.

    is_input_dir_a_date_folder = any(name.startswith("scandata_") for name in os.listdir(input_dir) if os.path.isdir(os.path.join(input_dir, name)))

    if is_input_dir_a_date_folder:
        potential_date_folders = [input_dir]
    else:
        potential_date_folders = [os.path.join(input_dir, d) for d in os.listdir(input_dir) if os.path.isdir(os.path.join(input_dir, d))]


    for date_folder_path in potential_date_folders:
        # Skip if it's not actually a directory (e.g. files in input_dir if not is_input_dir_a_date_folder)
        if not os.path.isdir(date_folder_path):
            continue
            
        # Skip if this sub-item doesn't contain scandata folders (relevant if input_dir was not a date_folder itself)
        if not any(name.startswith("scandata_") for name in os.listdir(date_folder_path) if os.path.isdir(os.path.join(date_folder_path, name))):
            # print(f"Info: Skipping '{date_folder_path}' as it does not appear to be a date folder with scandata.")
            continue


        for scandata_folder_name in os.listdir(date_folder_path):
            if scandata_folder_name.startswith("scandata_"):
                current_scandata_folder_path = os.path.join(date_folder_path, scandata_folder_name)
                pose_csv_path = os.path.join(current_scandata_folder_path, "pose.csv")

                if not os.path.exists(pose_csv_path):
                    print(f"Warning: 'pose.csv' not found in '{current_scandata_folder_path}'. Skipping.")
                    continue

                # Get the modification timestamp of the pose.csv file
                pose_file_mod_time = os.path.getmtime(pose_csv_path)
                pose_file_timestamp_str = datetime.fromtimestamp(pose_file_mod_time).strftime('%Y-%m-%d %H:%M:%S')

                try:
                    with open(pose_csv_path, 'r', encoding='utf-8', newline='') as f_pose:
                        reader = csv.DictReader(f_pose)
                        expected_pose_cols = {'date', 'scan', 'sequence', 'horizontal', 'vertical'}
                        
                        if not reader.fieldnames: # Handle empty pose.csv
                             print(f"Warning: 'pose.csv' in '{current_scandata_folder_path}' is empty or has no header. Skipping.")
                             continue

                        if not expected_pose_cols.issubset(reader.fieldnames):
                            print(f"Warning: 'pose.csv' in '{current_scandata_folder_path}' is missing one or more expected columns "
                                  f"(date, scan, sequence, horizontal, vertical). Found: {reader.fieldnames}. Skipping.")
                            continue
                        
                        for row_num, row in enumerate(reader):
                            # Validate that essential keys exist, even if empty, to avoid KeyErrors
                            # and to match the structure expected by DictWriter later.
                            output_row = {
                                "date": row.get("date"),
                                "scan_folder": row.get("scan"), 
                                "sequence": row.get("sequence"),
                                "horizontal": row.get("horizontal"),
                                "vertical": row.get("vertical"),
                                "pose_file_timestamp": pose_file_timestamp_str # Add the timestamp
                            }
                            # Add the constant health data
                            for i, header in enumerate(flat_health_headers):
                                output_row[header] = flat_health_values[i]
                            output_rows.append(output_row)
                except Exception as e:
                    print(f"Error reading or processing '{pose_csv_path}': {e}")

    if not output_rows:
        print("No data processed. Output CSV will not be created.")
        return

    try:
        with open(output_csv_file, 'w', encoding='utf-8', newline='') as f_out:
            writer = csv.DictWriter(f_out, fieldnames=csv_headers)
            writer.writeheader()
            writer.writerows(output_rows)
        print(f"Successfully created '{output_csv_file}' with {len(output_rows)} data rows.")
    except IOError as e:
        print(f"Error writing to output file '{output_csv_file}': {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Create a CSV file from scan data, combining pose information "
                    "with health status data from data_format.json and pose.csv timestamps."
    )
    parser.add_argument(
        "input_directory",
        help="The root directory containing the scan data (e.g., '20250604' or a folder containing multiple such date folders)."
    )
    parser.add_argument(
        "--output_csv",
        default=None, # Default is now None, will be derived from input_directory if not provided
        help="Path for the output CSV file. If not provided, defaults to '<input_directory_name>_summary.csv' "
             "(e.g., if input is '20250604', output is '20250604_summary.csv')."
    )

    args = parser.parse_args()
    
    output_file_path = args.output_csv
    if output_file_path is None:
        # Derive output filename from input directory name
        base_name = os.path.basename(os.path.normpath(args.input_directory))
        output_file_path = f"{base_name}_summary.csv"
        
    process_scan_data(args.input_directory, output_file_path) 