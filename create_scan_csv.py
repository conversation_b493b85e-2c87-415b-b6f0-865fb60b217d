import os
import csv
import json
import argparse
from datetime import datetime # Added for timestamp formatting

# Path to the data_format.json file, assuming it's in the same directory as the script
# or in the workspace root. This is now the fixed path.
DATA_FORMAT_JSON_PATH = "data_format.json"

def load_and_flatten_health_data():
    """Loads the health data from data_format.json (fixed path) and flattens it."""
    try:
        with open(DATA_FORMAT_JSON_PATH, 'r', encoding='utf-8') as f:
            health_data_template = json.load(f)
    except FileNotFoundError:
        print(f"Error: '{DATA_FORMAT_JSON_PATH}' not found. Please ensure it's in the correct location.")
        return None, None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{DATA_FORMAT_JSON_PATH}'.")
        return None, None

    flat_health_headers = []
    flat_health_values = []
    for main_key, sub_dict in health_data_template.items():
        for sub_key, value in sub_dict.items():
            flat_health_headers.append(f"{main_key}_{sub_key}")
            flat_health_values.append(value)
    return flat_health_headers, flat_health_values

def process_scan_directory(scan_dir, output_rows, flat_health_headers, flat_health_values):
    """
    Processes a scan_* directory with numeric subdirectories.
    Each numeric subdirectory contains params file with angle information.
    """
    # Extract date from scan directory name (e.g., scan_0709_102453 -> 0709)
    scan_dir_name = os.path.basename(scan_dir)
    date_part = "unknown"
    if scan_dir_name.startswith("scan_"):
        parts = scan_dir_name.split("_")
        if len(parts) >= 2:
            date_part = parts[1]  # e.g., "0709"

    # Get scan timestamp from directory modification time
    scan_timestamp = datetime.fromtimestamp(os.path.getmtime(scan_dir)).strftime('%Y-%m-%d %H:%M:%S')

    # Get all numeric subdirectories
    numeric_subdirs = []
    for item in os.listdir(scan_dir):
        item_path = os.path.join(scan_dir, item)
        if os.path.isdir(item_path) and item.isdigit():
            numeric_subdirs.append(item)

    # Sort numerically
    numeric_subdirs.sort(key=int)

    for subdir_name in numeric_subdirs:
        subdir_path = os.path.join(scan_dir, subdir_name)
        params_file_path = os.path.join(subdir_path, "params")

        # Parse angles from params file
        h_angle_str = "N/A"
        v_angle_str = "N/A"

        if os.path.exists(params_file_path):
            try:
                with open(params_file_path, 'r') as pf:
                    lines = pf.readlines()
                for line in lines:
                    stripped_line = line.strip()
                    if "H:" in stripped_line:
                        try:
                            parts = stripped_line.split("H:")
                            if len(parts) > 1:
                                h_angle_val = float(parts[1].strip())
                                h_angle_str = f"{h_angle_val:.2f}°"
                        except (ValueError, IndexError) as e:
                            print(f"Warning: Could not parse H angle from line: '{stripped_line}' in {params_file_path}. Error: {e}")
                    elif "V:" in stripped_line:
                        try:
                            parts = stripped_line.split("V:")
                            if len(parts) > 1:
                                v_angle_val = float(parts[1].strip())
                                v_angle_str = f"{v_angle_val:.2f}°"
                        except (ValueError, IndexError) as e:
                            print(f"Warning: Could not parse V angle from line: '{stripped_line}' in {params_file_path}. Error: {e}")
            except Exception as e:
                print(f"Error reading params file {params_file_path}: {e}")
        else:
            print(f"Warning: Params file not found: {params_file_path}")

        # Create output row
        output_row = {
            "date": date_part,
            "scan_folder": scan_dir_name,
            "sequence": int(subdir_name) + 1,  # Convert to 1-based sequence
            "horizontal": h_angle_str,
            "vertical": v_angle_str,
            "scan_timestamp": scan_timestamp
        }

        # Add health data
        for i, header in enumerate(flat_health_headers):
            output_row[header] = flat_health_values[i]

        output_rows.append(output_row)

def process_pose_csv(pose_csv_path, scandata_folder_path, output_rows, flat_health_headers, flat_health_values):
    """
    Processes a pose.csv file from the old scandata structure.
    """
    # Get the modification timestamp of the pose.csv file
    pose_file_mod_time = os.path.getmtime(pose_csv_path)
    pose_file_timestamp_str = datetime.fromtimestamp(pose_file_mod_time).strftime('%Y-%m-%d %H:%M:%S')

    try:
        with open(pose_csv_path, 'r', encoding='utf-8', newline='') as f_pose:
            reader = csv.DictReader(f_pose)
            expected_pose_cols = {'date', 'scan', 'sequence', 'horizontal', 'vertical'}

            if not reader.fieldnames:
                print(f"Warning: 'pose.csv' in '{scandata_folder_path}' is empty or has no header. Skipping.")
                return

            if not expected_pose_cols.issubset(reader.fieldnames):
                print(f"Warning: 'pose.csv' in '{scandata_folder_path}' is missing one or more expected columns "
                      f"(date, scan, sequence, horizontal, vertical). Found: {reader.fieldnames}. Skipping.")
                return

            for row_num, row in enumerate(reader):
                output_row = {
                    "date": row.get("date"),
                    "scan_folder": row.get("scan"),
                    "sequence": row.get("sequence"),
                    "horizontal": row.get("horizontal"),
                    "vertical": row.get("vertical"),
                    "scan_timestamp": pose_file_timestamp_str
                }
                # Add the constant health data
                for i, header in enumerate(flat_health_headers):
                    output_row[header] = flat_health_values[i]
                output_rows.append(output_row)
    except Exception as e:
        print(f"Error reading or processing '{pose_csv_path}': {e}")

def process_scan_data(input_dir, output_csv_file):
    """
    Processes scan data from the input directory and generates a summary CSV file.
    This function now handles both the old scandata structure and the new scan_* structure.
    For scan_* directories, it processes numeric subdirectories and extracts angle data from params files.
    """
    flat_health_headers, flat_health_values = load_and_flatten_health_data()
    if flat_health_headers is None:
        return

    # Base headers - updated for new structure
    base_headers = ["date", "scan_folder", "sequence", "horizontal", "vertical", "scan_timestamp"]
    csv_headers = base_headers + flat_health_headers
    output_rows = []

    if not os.path.isdir(input_dir):
        print(f"Error: Input directory '{input_dir}' not found or is not a directory.")
        return

    # Check if this is a scan_* directory (new structure) or contains scandata_* (old structure)
    input_dir_name = os.path.basename(input_dir)

    if input_dir_name.startswith("scan_"):
        # New structure: process scan_* directory directly
        print(f"Processing new scan structure: {input_dir}")
        process_scan_directory(input_dir, output_rows, flat_health_headers, flat_health_values)
    else:
        # Old structure: look for date folders containing scandata_*
        potential_date_folders = []
        is_input_dir_a_date_folder = any(name.startswith("scandata_") for name in os.listdir(input_dir) if os.path.isdir(os.path.join(input_dir, name)))

        if is_input_dir_a_date_folder:
            potential_date_folders = [input_dir]
        else:
            potential_date_folders = [os.path.join(input_dir, d) for d in os.listdir(input_dir) if os.path.isdir(os.path.join(input_dir, d))]

        # Process old structure
        for date_folder_path in potential_date_folders:
            if not os.path.isdir(date_folder_path):
                continue

            if not any(name.startswith("scandata_") for name in os.listdir(date_folder_path) if os.path.isdir(os.path.join(date_folder_path, name))):
                continue

            for scandata_folder_name in os.listdir(date_folder_path):
                if scandata_folder_name.startswith("scandata_"):
                    current_scandata_folder_path = os.path.join(date_folder_path, scandata_folder_name)
                    pose_csv_path = os.path.join(current_scandata_folder_path, "pose.csv")

                    if not os.path.exists(pose_csv_path):
                        print(f"Warning: 'pose.csv' not found in '{current_scandata_folder_path}'. Skipping.")
                        continue

                    process_pose_csv(pose_csv_path, current_scandata_folder_path, output_rows, flat_health_headers, flat_health_values)

    if not output_rows:
        print("No data processed. Output CSV will not be created.")
        return

    try:
        with open(output_csv_file, 'w', encoding='utf-8', newline='') as f_out:
            writer = csv.DictWriter(f_out, fieldnames=csv_headers)
            writer.writeheader()
            writer.writerows(output_rows)
        print(f"Successfully created '{output_csv_file}' with {len(output_rows)} data rows.")
    except IOError as e:
        print(f"Error writing to output file '{output_csv_file}': {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Create a CSV file from scan data, combining pose information "
                    "with health status data from data_format.json and pose.csv timestamps."
    )
    parser.add_argument(
        "input_directory",
        help="The root directory containing the scan data (e.g., '20250604' or a folder containing multiple such date folders)."
    )
    parser.add_argument(
        "--output_csv",
        default=None, # Default is now None, will be derived from input_directory if not provided
        help="Path for the output CSV file. If not provided, defaults to '<input_directory_name>_summary.csv' "
             "(e.g., if input is '20250604', output is '20250604_summary.csv')."
    )

    args = parser.parse_args()
    
    output_file_path = args.output_csv
    if output_file_path is None:
        # Derive output filename from input directory name
        base_name = os.path.basename(os.path.normpath(args.input_directory))
        output_file_path = f"{base_name}_summary.csv"
        
    process_scan_data(args.input_directory, output_file_path) 